# 随机数排序程序 - miniRV指令集实现
# 使用线性反馈移位寄存器生成随机数
# 5个阶段的状态机实现

.data
    # 程序变量
    current_stage:  .word 0             # 当前阶段 (0-4)
    random_seed:    .word 0             # 随机数种子
    lfsr_state:     .word 0             # LFSR状态
    random_array:   .word 0, 0, 0, 0, 0, 0, 0, 0  # 8个4bit随机数数组
    sorted_flag:    .word 0             # 排序完成标志

.text
.globl main

main:
    # 初始化栈指针
    lui sp, 0x10001

    # 初始化寄存器
    addi s0, zero, 0        # s0 = current_stage
    addi s1, zero, 0        # s1 = random_seed
    addi s2, zero, 0        # s2 = lfsr_state

    # 加载外设基地址 0xFFFFF000
    lui s3, 0xFFFFF         # s3 = 外设基地址

    # 初始化计时器分频系数
    addi t0, zero, 1        # 设置分频系数为1（频率=时钟/2）
    sw t0, 0x24(s3)         # 写入分频系数到0xFFFFF024

main_loop:
    # 读取拨码开关状态 (0xFFFFF070)
    lw t1, 0x70(s3)         # 读取开关状态
    andi t1, t1, 3          # 取最低2位 SW[1:0]

    # 根据当前阶段跳转
    beq s0, zero, stage0    # 阶段0
    addi t2, zero, 1
    beq s0, t2, stage1      # 阶段1
    addi t2, zero, 2
    beq s0, t2, stage2      # 阶段2
    addi t2, zero, 3
    beq s0, t2, stage3      # 阶段3
    addi t2, zero, 4
    beq s0, t2, stage4      # 阶段4
    jal zero, main_loop

# 阶段0：显示计时器值，等待SW[1:0]==1
stage0:
    # 读取并显示计时器值
    # 0xFFFFF020: 读取计时器值 (读/写)
    lw t0, 0x20(s3)         # 读取32位计时器值
    sw t0, 0x00(s3)         # 显示到数码管 (0xFFFFF000)

    # 检查是否进入阶段1
    addi t2, zero, 1
    beq t1, t2, enter_stage1
    jal zero, main_loop

enter_stage1:
    addi s0, zero, 1        # 进入阶段1
    lw s1, 0x20(s3)         # 从0xFFFFF020读取计时器值作为随机数种子
    addi s2, s1, 0          # 初始化LFSR状态
    jal zero, main_loop

# 阶段1：显示种子值，等待SW[1:0]==2
stage1:
    # 显示种子值
    sw s1, 0x00(s3)         # 显示种子到数码管

    # 检查是否进入阶段2
    addi t2, zero, 2
    beq t1, t2, enter_stage2
    jal zero, main_loop

enter_stage2:
    addi s0, zero, 2        # 进入阶段2
    jal zero, main_loop

# 阶段2：生成并显示随机数数组，等待SW[1:0]==3
stage2:
    # 生成8个4bit随机数
    jal ra, generate_random_array

    # 将数组打包显示到数码管
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # 显示到数码管

    # 检查是否进入阶段3
    addi t2, zero, 3
    beq t1, t2, enter_stage3

    # 返回主循环继续生成随机数
    jal zero, main_loop

enter_stage3:
    addi s0, zero, 3        # 进入阶段3
    # 重置sorted_flag，准备排序
    lui t5, 0x10010
    sw zero, 64(t5)         # sorted_flag = 0

    # 清除LED，准备进入阶段3
    sw zero, 0x60(s3)       # 清除LED
    jal zero, stage3

# 阶段3：排序数组，等待SW[1:0]==0
stage3:
    # 执行冒泡排序
    jal ra, bubble_sort_array

    # 点亮LED[0]表示排序完成 (0xFFFFF060)
    addi t0, zero, 1
    sw t0, 0x60(s3)         # 点亮LED[0]

    beq t1, zero, enter_stage4

enter_stage4:
    addi s0, zero, 4        # 进入阶段4
    jal zero, main_loop

# 阶段4：显示排序后的数组，程序结束
stage4:
    # 显示排序后的数组
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # 显示到数码管
    jal zero, stage4        # 保持显示

# 生成8个4bit随机数的函数
generate_random_array:
    addi sp, sp, -16
    sw ra, 12(sp)
    sw s7, 8(sp)
    sw s8, 4(sp)
    sw s9, 0(sp)

    lui s7, 0x10010         # 数组基地址
    addi s7, s7, 32         # random_array地址偏移
    addi s8, zero, 0        # 循环计数器
    addi s9, zero, 8        # 数组大小

gen_loop:
    # 调用LFSR生成随机数
    jal ra, lfsr_next
    andi t0, t0, 15         # 取4bit (0-15)

    # 存储到数组
    slli t1, s8, 2          # 计算偏移量 (i*4)
    add t2, s7, t1          # 计算地址
    sw t0, 0(t2)            # 存储随机数

    addi s8, s8, 1          # 计数器+1
    blt s8, s9, gen_loop    # 继续循环

    lw s9, 0(sp)
    lw s8, 4(sp)
    lw s7, 8(sp)
    lw ra, 12(sp)
    addi sp, sp, 16
    jalr zero, ra, 0

# 线性反馈移位寄存器 (LFSR) 函数
# 使用16位LFSR，多项式: x^16 + x^14 + x^13 + x^11 + 1
lfsr_next:
    # 计算反馈位
    srli t0, s2, 0          # bit 0
    srli t1, s2, 2          # bit 2
    srli t2, s2, 3          # bit 3
    srli t3, s2, 5          # bit 5

    andi t0, t0, 1
    andi t1, t1, 1
    andi t2, t2, 1
    andi t3, t3, 1

    xor t0, t0, t1
    xor t0, t0, t2
    xor t0, t0, t3          # 反馈位

    # 左移并插入反馈位
    slli s2, s2, 1
    or s2, s2, t0
    # 保持16位 - 使用多条指令
    lui t4, 0x1
    addi t4, t4, -1         # t4 = 0xFFFF
    and s2, s2, t4

    # 防止全零状态
    bne s2, zero, lfsr_done
    addi s2, zero, 1        # 如果为0，设为1

lfsr_done:
    addi t0, s2, 0          # 返回当前LFSR值
    jalr zero, ra, 0

# 将数组打包为显示格式的函数
pack_array_for_display:
    addi sp, sp, -12
    sw ra, 8(sp)
    sw s7, 4(sp)
    sw s8, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array地址
    addi t0, zero, 0        # 结果初始化
    addi s8, zero, 0        # 循环计数器

pack_loop:
    slli t1, s8, 2          # 计算偏移量
    add t2, s7, t1          # 计算地址
    lw t3, 0(t2)            # 读取数组元素
    andi t3, t3, 15         # 确保4位

    slli t1, s8, 2          # 每个4位元素占用i*4位
    sll t3, t3, t1          # 左移到正确位置
    or t0, t0, t3           # 合并到结果

    addi s8, s8, 1
    addi t4, zero, 8
    blt s8, t4, pack_loop

    lw s8, 0(sp)
    lw s7, 4(sp)
    lw ra, 8(sp)
    addi sp, sp, 12
    jalr zero, ra, 0

# 冒泡排序函数
bubble_sort_array:
    addi sp, sp, -20
    sw ra, 16(sp)
    sw s7, 12(sp)
    sw s8, 8(sp)
    sw s9, 4(sp)
    sw s10, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array地址
    addi s8, zero, 7        # 外层循环计数器 (n-1)

outer_loop:
    addi s9, zero, 0        # 内层循环计数器

inner_loop:
    # 计算当前元素地址
    slli t1, s9, 2
    add t2, s7, t1
    lw t3, 0(t2)            # 当前元素
    lw t4, 4(t2)            # 下一个元素

    # 比较并交换
    blt t3, t4, no_swap
    sw t4, 0(t2)            # 交换
    sw t3, 4(t2)

no_swap:
    addi s9, s9, 1
    blt s9, s8, inner_loop

    addi s8, s8, -1
    blt zero, s8, outer_loop

    lw s10, 0(sp)
    lw s9, 4(sp)
    lw s8, 8(sp)
    lw s7, 12(sp)
    lw ra, 16(sp)
    addi sp, sp, 20
    jalr zero, ra, 0
