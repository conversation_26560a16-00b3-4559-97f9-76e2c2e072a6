# 阶段3修复测试程序

.data
    test_array: .word 8, 3, 6, 1, 9, 2, 7, 4  # 测试数组
    sorted_flag: .word 0

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    addi s1, zero, 2        # 模拟从阶段2开始

main_loop:
    # 读取开关状态
    lw t1, 0x70(s0)
    andi t1, t1, 3          # SW[1:0]
    
    # 显示状态：高16位=阶段，低16位=开关
    slli t2, s1, 16
    or t2, t2, t1
    sw t2, 0x00(s0)         # 显示状态
    
    # 阶段跳转
    addi t3, zero, 2
    beq s1, t3, test_stage2
    addi t3, zero, 3
    beq s1, t3, test_stage3
    addi t3, zero, 4
    beq s1, t3, test_stage4
    jal zero, main_loop

test_stage2:
    # 阶段2：模拟显示随机数
    # 显示固定值模拟随机数变化
    lw t4, 0x20(s0)         # 读取计时器作为"随机数"
    sw t4, 0x00(s0)         # 显示到数码管
    
    # 检查是否进入阶段3
    addi t3, zero, 3
    beq t1, t3, enter_stage3
    jal zero, main_loop

enter_stage3:
    addi s1, zero, 3        # 进入阶段3
    # 重置排序标志
    lui t5, 0x10010
    sw zero, 64(t5)
    jal zero, main_loop

test_stage3:
    # 阶段3：排序并等待
    lui t5, 0x10010
    lw t6, 64(t5)           # 检查sorted_flag
    bne t6, zero, stage3_wait
    
    # 模拟排序（简化）
    addi t0, zero, 1
    sw t0, 0x60(s0)         # 点亮LED[0]
    sw t0, 64(t5)           # 设置sorted_flag = 1
    
    # 显示排序完成标志
    addi t0, zero, 0xAAAA
    sw t0, 0x00(s0)         # 显示特殊值表示排序完成

stage3_wait:
    # 重新读取开关状态（关键修复）
    lw t1, 0x70(s0)
    andi t1, t1, 3
    
    # 更新显示（阶段3 + 开关状态）
    addi t2, zero, 3
    slli t2, t2, 16
    or t2, t2, t1
    sw t2, 0x00(s0)
    
    # 检查是否进入阶段4
    beq t1, zero, enter_stage4
    jal zero, stage3_wait   # 继续等待，不返回main_loop

enter_stage4:
    addi s1, zero, 4
    jal zero, main_loop

test_stage4:
    # 阶段4：显示排序结果
    addi t0, zero, 0x12345678
    sw t0, 0x00(s0)
    jal zero, test_stage4
