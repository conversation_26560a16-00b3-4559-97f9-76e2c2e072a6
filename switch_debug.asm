# 开关调试程序 - 检查开关状态和阶段转换

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    addi s1, zero, 2        # 模拟当前在阶段2
    
debug_loop:
    # 读取开关状态
    lw t0, 0x70(s0)         # 读取完整开关状态
    andi t1, t0, 3          # 取SW[1:0]
    
    # 显示开关状态到数码管
    # 格式：高位显示完整开关值，低位显示SW[1:0]
    slli t2, t0, 16         # 完整开关值左移16位
    or t2, t2, t1           # 合并SW[1:0]到低位
    sw t2, 0x00(s0)         # 显示到数码管
    
    # 检查不同的开关状态
    beq t1, zero, show_0    # SW[1:0] = 00
    addi t3, zero, 1
    beq t1, t3, show_1      # SW[1:0] = 01
    addi t3, zero, 2  
    beq t1, t3, show_2      # SW[1:0] = 10
    addi t3, zero, 3
    beq t1, t3, show_3      # SW[1:0] = 11
    jal zero, debug_loop
    
show_0:
    # SW[1:0] = 00，点亮LED位0
    addi t4, zero, 1
    sw t4, 0x60(s0)
    jal zero, debug_loop
    
show_1:
    # SW[1:0] = 01，点亮LED位1
    addi t4, zero, 2
    sw t4, 0x60(s0)
    jal zero, debug_loop
    
show_2:
    # SW[1:0] = 10，点亮LED位2
    addi t4, zero, 4
    sw t4, 0x60(s0)
    jal zero, debug_loop
    
show_3:
    # SW[1:0] = 11，点亮LED位3
    addi t4, zero, 8
    sw t4, 0x60(s0)
    jal zero, debug_loop
