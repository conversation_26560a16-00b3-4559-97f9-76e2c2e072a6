# LED测试程序
# 测试LED[0]的点亮功能

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址 0xFFFFF000
    
    # 读取拨码开关
    lw t0, 0x70(s0)         # 从0xFFFFF070读取开关
    andi t1, t0, 1          # 检查SW[0]
    
    # 如果SW[0]=1，点亮LED[0]
    beq t1, zero, led_off
    
led_on:
    addi t2, zero, 1
    sw t2, 0x60(s0)         # 点亮LED[0] (写入0xFFFFF060)
    jal zero, display_status
    
led_off:
    sw zero, 0x60(s0)       # 熄灭LED[0]
    
display_status:
    # 在数码管显示开关状态
    sw t0, 0x00(s0)         # 显示到数码管 (0xFFFFF000)
    
    # 继续循环
    jal zero, main
