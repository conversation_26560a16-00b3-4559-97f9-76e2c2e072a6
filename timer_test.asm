# 计时器测试程序
# 测试计时器的两个端口功能

.text
main:
    # 初始化栈指针
    lui sp, 0x10001
    
    # 加载外设基地址
    lui s0, 0xFFFFF         # s0 = 0xFFFFF000
    
    # 设置计时器分频系数
    addi t0, zero, 10       # 分频系数设为10
    sw t0, 0x24(s0)         # 写入0xFFFFF024 (设置分频系数)
    
    # 可选：重置计时器值
    addi t1, zero, 0
    sw t1, 0x20(s0)         # 写入0xFFFFF020 (重置计时器)
    
test_loop:
    # 读取计时器当前值
    lw t2, 0x20(s0)         # 从0xFFFFF020读取计时器值
    
    # 显示到数码管
    sw t2, 0x00(s0)         # 显示到0xFFFFF000
    
    # 读取拨码开关
    lw t3, 0x70(s0)         # 从0xFFFFF070读取开关状态
    andi t3, t3, 1          # 检查SW[0]
    
    # 如果SW[0]=1，则退出
    bne t3, zero, exit_program
    
    # 继续循环
    jal zero, test_loop

exit_program:
    # 程序结束
    addi a7, zero, 10
    ecall
