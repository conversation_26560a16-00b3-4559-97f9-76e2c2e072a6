# LED调试程序 - 测试LED是否能正常工作

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    
test_loop:
    # 读取拨码开关
    lw t0, 0x70(s0)         # 读取开关状态
    andi t1, t0, 1          # 检查SW[0]
    andi t2, t0, 2          # 检查SW[1]
    
    # 显示开关状态到数码管
    sw t0, 0x00(s0)
    
    # 根据开关状态控制LED
    beq t1, zero, led0_off
    # SW[0]=1时点亮LED[0]
    addi t3, zero, 1
    sw t3, 0x60(s0)         # 点亮LED[0]
    jal zero, check_sw1
    
led0_off:
    # SW[0]=0时熄灭LED[0]
    sw zero, 0x60(s0)       # 熄灭LED[0]
    
check_sw1:
    # 可以添加更多LED测试
    # 比如用SW[1]控制其他LED位
    
    jal zero, test_loop
