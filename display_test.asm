# 数码管显示测试程序
# 测试8个4位随机数的显示

.data
    test_array: .word 1, 2, 3, 4, 5, 6, 7, 8  # 测试数组

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    
    # 测试显示固定数组
    jal ra, pack_test_array
    sw t0, 0x00(s0)         # 显示到数码管
    
    # 无限循环保持显示
loop:
    jal zero, loop

# 打包测试数组为显示格式
pack_test_array:
    addi sp, sp, -8
    sw ra, 4(sp)
    sw s1, 0(sp)
    
    lui s1, 0x10010         # 数组基地址
    addi t0, zero, 0        # 结果初始化
    addi t1, zero, 0        # 循环计数器

pack_test_loop:
    # 计算数组元素地址
    slli t2, t1, 2          # i*4
    add t3, s1, t2          # 数组地址
    lw t4, 0(t3)            # 读取元素
    andi t4, t4, 15         # 确保4位
    
    # 计算位移量 (i*4位)
    slli t2, t1, 2          # i*4
    sll t4, t4, t2          # 左移到正确位置
    or t0, t0, t4           # 合并到结果
    
    addi t1, t1, 1          # 计数器+1
    addi t5, zero, 8
    blt t1, t5, pack_test_loop
    
    lw s1, 0(sp)
    lw ra, 4(sp)
    addi sp, sp, 8
    jalr zero, ra, 0
