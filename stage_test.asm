# 阶段转换测试程序

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    addi s1, zero, 2        # 当前阶段 = 2
    
main_loop:
    # 读取开关状态
    lw t1, 0x70(s0)         # 读取开关
    andi t1, t1, 3          # 取SW[1:0]
    
    # 显示当前状态：高16位=当前阶段，低16位=开关状态
    slli t2, s1, 16         # 当前阶段左移16位
    or t2, t2, t1           # 合并开关状态
    sw t2, 0x00(s0)         # 显示到数码管
    
    # 根据当前阶段跳转
    addi t3, zero, 2
    beq s1, t3, test_stage2
    addi t3, zero, 3
    beq s1, t3, test_stage3
    jal zero, main_loop

test_stage2:
    # 模拟阶段2：等待SW[1:0]==3进入阶段3
    addi t3, zero, 3
    beq t1, t3, enter_stage3
    
    # 点亮LED表示在阶段2
    addi t4, zero, 2
    sw t4, 0x60(s0)
    jal zero, main_loop

enter_stage3:
    # 进入阶段3
    addi s1, zero, 3
    jal zero, main_loop

test_stage3:
    # 模拟阶段3：排序并点亮LED[0]
    addi t4, zero, 1
    sw t4, 0x60(s0)         # 点亮LED[0]表示排序完成
    
    # 等待SW[1:0]==0进入阶段4
    beq t1, zero, enter_stage4
    jal zero, main_loop

enter_stage4:
    # 进入阶段4
    addi s1, zero, 4
    # 点亮更多LED表示进入阶段4
    addi t4, zero, 15       # 点亮多个LED
    sw t4, 0x60(s0)
    jal zero, main_loop
