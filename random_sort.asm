# 系统初始化
    li   sp, 0x10000          # 初始化栈指针 (0x10000)
    lui  s1, 0xFFFFF          # 设置I/O基地址 (0xFFFFF000)
    addi s0, sp, -4           # 设置栈边界 (sp - 4)

# 阶段0：显示计时器值
# 功能：循环读取32位计时器值并显示
STAGE0:
    lw   t0, 0x20(s1)         # 读取计时器值 (I/O地址 0x20)
    sw   t0, 0x00(s1)         # 显示到数码管 (I/O地址 0x00)
    
    lw   a0, 0x70(s1)         # 读取开关状态 (I/O地址 0x70)
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 1          # 设置目标状态：阶段1
    bne  a0, a1, STAGE0       # 循环直到SW[1:0] == 1

# 阶段1：显示随机数种子
# 功能：将计时器值作为种子显示
STAGE1:
    sw   t0, 0x00(s1)         # 显示种子值（阶段0最后读取的计时器值）
    
    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 2          # 设置目标状态：阶段2
    bne  a0, a1, STAGE1       # 循环直到SW[1:0] == 2

# 阶段2：生成并显示随机数数组
# 功能：使用种子生成8个4位随机数，并显示32位组合值
    addi sp, sp, -32          # 栈上分配32字节空间(8x4字节)
STAGE2_LOOP:
#(LFSR)生成伪随机数
    andi a2, t0, 1            # 提取bit0
    srli a3, t0, 1            # 
    andi a3, a3, 1            # 提取bit1
    xor  a2, a2, a3           # bit0 XOR bit1
    srli a4, t0, 21           # 
    andi a4, a4, 1            # 提取bit21
    xor  a2, a2, a4           # 与之前结果异或
    srli a5, t0, 31           # 
    andi a5, a5, 1            # 提取bit31(MSB)
    xor  a2, a2, a5           # 得到新LSB(反馈位)

    slli t0, t0, 1            # 左移1位
    add  t0, t0, a2           # 插入反馈位到LSB
    
#显示生成的32位随机数
    sw   t0, 0x00(s1)         # 输出到数码管
    
#拆分32位数到8个4位数字
    mv   t3, t0               # 复制随机数用于拆分
    andi a2, t3, 15           # 数字0: bits[3:0]
    sw   a2, 0(sp)            # 存储到栈
    srli t3, t3, 4            # 
    andi a3, t3, 15           # 数字1: bits[7:4]
    sw   a3, 4(sp)            # 
    srli t3, t3, 4            # 
    andi a4, t3, 15           # 数字2: bits[11:8]
    sw   a4, 8(sp)            # 
    srli t3, t3, 4            # 
    andi a5, t3, 15           # 数字3: bits[15:12]
    sw   a5, 12(sp)           # 
    srli t3, t3, 4            # 
    andi a6, t3, 15           # 数字4: bits[19:16]
    sw   a6, 16(sp)           # 
    srli t3, t3, 4            # 
    andi a7, t3, 15           # 数字5: bits[23:20]
    sw   a7, 20(sp)           # 
    srli t3, t3, 4            # 
    andi s2, t3, 15           # 数字6: bits[27:24]
    sw   s2, 24(sp)           # 
    srli t3, t3, 4            # 
    andi s3, t3, 15           # 数字7: bits[31:28]
    sw   s3, 28(sp)           # 
    
#检查状态转换
    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    addi a1, zero, 3          # 设置目标状态：阶段3
    bne  a0, a1, STAGE2_LOOP  # 循环直到SW[1:0] == 3


# 阶段3：排序随机数数组
# 功能：对栈中的8个4位数字进行冒泡排序
    addi s0, sp, 32           # 设置数组边界(sp + 32)
    addi t1, zero, 8          # 外循环计数器(元素数量=8)
OUTER_LOOP:
    addi t2, sp, 0            # 内循环指针(数组起始)
INNER_LOOP:
    lw   s4, 0(t2)            # 加载当前元素
    lw   s5, 4(t2)            # 加载下一元素
    
#比较并交换元素
    bge  s4, s5, NO_SWAP      # 若顺序正确则跳过交换
    sw   s4, 4(t2)            # 交换元素位置
    sw   s5, 0(t2)            # 
NO_SWAP:
    addi t2, t2, 4            # 移动到下一元素
    blt  t2, s0, INNER_LOOP   # 内循环直到数组结束
    
    addi t1, t1, -1           # 外循环计数减1
    bgt  t1, zero, OUTER_LOOP # 继续外循环
    
#排序完成信号
    addi t3, zero, 1          # 
    sw   t3, 0x60(s1)         # 点亮LED[0] (I/O地址 0x60)
    
#等待状态转换
STAGE3_WAIT:
    lw   a0, 0x70(s1)         # 读取开关状态
    andi a0, a0, 0x3          # 提取SW[1:0]
    bne  a0, zero, STAGE3_WAIT # 循环直到SW[1:0] == 0

# 阶段4：显示排序结果
# 功能：组合排序后的8个4位数字并显示

#从栈中加载排序后的数字
    lw   a2, 0(sp)            # 加载数字0
    lw   a3, 4(sp)            # 加载数字1
    lw   a4, 8(sp)            # 加载数字2
    lw   a5, 12(sp)           # 加载数字3
    lw   a6, 16(sp)           # 加载数字4
    lw   a7, 20(sp)           # 加载数字5
    lw   s2, 24(sp)           # 加载数字6
    lw   s3, 28(sp)           # 加载数字7
    
#组合为32位数
    add  t0, zero, zero       # 初始化结果寄存器
    add  t0, t0, a2           # 添加数字0
    slli t0, t0, 4            # 左移4位
    add  t0, t0, a3           # 添加数字1
    slli t0, t0, 4            # 
    add  t0, t0, a4           # 添加数字2
    slli t0, t0, 4            # 
    add  t0, t0, a5           # 添加数字3
    slli t0, t0, 4            # 
    add  t0, t0, a6           # 添加数字4
    slli t0, t0, 4            # 
    add  t0, t0, a7           # 添加数字5
    slli t0, t0, 4            # 
    add  t0, t0, s2           # 添加数字6
    slli t0, t0, 4            # 
    add  t0, t0, s3           # 添加数字7
    
    addi sp, sp, 32           # 释放栈空间
    sw   t0, 0x00(s1)         # 显示排序结果
    
END:
    jal  zero, END            # 永久循环保持显示状态