# 阶段3测试程序 - 验证排序和LED功能

.data
    test_array: .word 8, 3, 6, 1, 9, 2, 7, 4  # 测试数组
    sorted_flag: .word 0

.text
main:
    # 初始化
    lui sp, 0x10001
    lui s0, 0xFFFFF         # 外设基地址
    
    # 显示原始数组
    jal ra, pack_and_display
    
    # 等待用户按下开关
wait_switch:
    lw t0, 0x70(s0)         # 读取开关
    andi t0, t0, 1          # 检查SW[0]
    beq t0, zero, wait_switch
    
    # 执行排序
    jal ra, test_bubble_sort
    
    # 点亮LED[0]
    addi t0, zero, 1
    sw t0, 0x60(s0)         # 点亮LED[0]
    
    # 显示排序后的数组
    jal ra, pack_and_display
    
    # 无限循环
end_loop:
    jal zero, end_loop

# 简化的冒泡排序
test_bubble_sort:
    addi sp, sp, -16
    sw ra, 12(sp)
    sw s1, 8(sp)
    sw s2, 4(sp)
    sw s3, 0(sp)
    
    lui s1, 0x10010         # 数组基地址
    addi s2, zero, 7        # 外层循环计数器 (n-1)

outer:
    addi s3, zero, 0        # 内层循环计数器
    
inner:
    # 计算当前元素地址
    slli t1, s3, 2
    add t2, s1, t1
    lw t3, 0(t2)            # 当前元素
    lw t4, 4(t2)            # 下一个元素
    
    # 比较并交换
    blt t3, t4, no_swap
    sw t4, 0(t2)            # 交换
    sw t3, 4(t2)
    
no_swap:
    addi s3, s3, 1
    blt s3, s2, inner
    
    addi s2, s2, -1
    blt zero, s2, outer
    
    lw s3, 0(sp)
    lw s2, 4(sp)
    lw s1, 8(sp)
    lw ra, 12(sp)
    addi sp, sp, 16
    jalr zero, ra, 0

# 打包并显示数组
pack_and_display:
    addi sp, sp, -8
    sw ra, 4(sp)
    sw s1, 0(sp)
    
    lui s1, 0x10010         # 数组基地址
    addi t0, zero, 0        # 结果初始化
    addi t1, zero, 0        # 循环计数器

pack_loop:
    slli t2, t1, 2          # 计算偏移量
    add t3, s1, t2          # 计算地址
    lw t4, 0(t3)            # 读取数组元素
    andi t4, t4, 15         # 确保4位
    
    slli t2, t1, 2          # 每个元素占4位
    sll t4, t4, t2          # 左移到正确位置
    or t0, t0, t4           # 合并到结果
    
    addi t1, t1, 1
    addi t5, zero, 8
    blt t1, t5, pack_loop
    
    # 显示到数码管
    sw t0, 0x00(s0)
    
    lw s1, 0(sp)
    lw ra, 4(sp)
    addi sp, sp, 8
    jalr zero, ra, 0
